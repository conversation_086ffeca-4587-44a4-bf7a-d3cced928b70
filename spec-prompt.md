<Instructions>
- You are to follow the instruction below precicely to create a solution that meets the objective.
- The context section will list the files you can edit, the programming language to use, and any other key constraints.
- The taskswill give information on the activites to undertake.
- If there any custom instructions these will be described in blocks
- Text inside of {{}} is a placeholder and should be replaced with data in the represented format/layout
</Instructions>


<Name>
   spacy NLP/ER processor - simple apple silicon proof of concept
</Name>

<Objective>
- demonstrate a very simple entity / concept extractor using spacy
- the extractor must be optimisded for apple silicon making use of the apple gpu
- the extractor should be multi threaded and threadsafe
- the extractor should have a simple chunk size configuration  - e.g. main.py --chunk 1000
- the extractor will use the file /Users/<USER>/Dev-Space/pythonProjects/spacy-test/ThirdPartyNoticeText.txt
- the extractor should have simple thread config - e.g. main.py --worker 8
- the extractor should support both spaCy and Hugging Face transformers (e.g. BERT) as selectable options via a command line switch (e.g. --sp for spaCy, --hf for Hugging Face/BERT)
</Objective>

<Context>
- i'm building a local graphRAG solution need a fast efficient local NLP/ER option
- it must run on apple silicon and be optimised for the apple GPU
- this is only a concept to show the dependencies needed, the configuration, and the balance between chunks and recognition
- extracted concepts, entities and relationships should be save in ./output/{{yyyy-mm-dd hh:mm}}-output.json
</Context>

<Tasks>
1. Initialize Git repository and main branch
2. Set up Python project structure (main.py, requirements.txt, output/)
3. Add spaCy and Apple Silicon dependencies to requirements.txt
3.1. Create Python virtual environment (venv) to isolate dependencies
4. Implement command-line argument parsing for --chunk and --worker
5. Implement file chunking logic for ThirdPartyNoticeText.txt
6. Set up multi-threaded processing using threading or multiprocessing
7. Configure spaCy for Apple Silicon and GPU usage
8. Load and test spaCy NER pipeline
9. Process text chunks and extract entities/concepts
10. Ensure thread safety in entity extraction
11. Aggregate and format extracted data from all threads
12. Save output to timestamped JSON file in ./output/
13. Add basic error handling and logging
14. Write a README with setup and usage instructions
15. Add a test script or test cases for pipeline verification
16. Implement atomic feature branch workflow for each task
17. (Optional) Add CI for linting and tests
18. Add Hugging Face transformers (e.g. BERT) as an alternative extraction backend
19. Implement command-line switch to select between spaCy (--sp) and Hugging Face (--hf) backends
20. Integrate Hugging Face pipeline for entity/concept extraction and ensure output format matches spaCy mode
21. Optimize Hugging Face pipeline for Apple Silicon if possible
22. Add tests and usage documentation for Hugging Face mode
</Tasks>


<Blocks>

</Blocks>
