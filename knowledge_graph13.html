<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Analysis Report</title>
    <style>
        body { font-family: sans-serif; display: flex; flex-direction: column; align-items: center; }
        #report-container { display: flex; width: 98%; gap: 16px; margin-top: 20px; }
        #graph-container { flex-grow: 1; border: 1px solid #ccc; padding: 10px; border-radius: 8px; }
        #sidebar { width: 400px; flex-shrink: 0; border: 1px solid #ccc; padding: 10px; border-radius: 8px; max-height: 80vh; overflow-y: auto; }
        h1, h2 { color: #333; }
        #analysis-report ul { list-style-type: none; padding-left: 0; }
        #analysis-report h3 { border-bottom: 1px solid #eee; padding-bottom: 5px; margin-top: 20px;}
        #analysis-report li { background-color: #f9f9f9; border: 1px solid #eee; padding: 8px; margin-bottom: 5px; border-radius: 4px; }
        #analysis-report strong { color: #c0392b; }
    </style>
</head>
<body>
    <h1>Codebase Analysis Report</h1>
    <div id="report-container">
        <div id="graph-container">
            <h2>Knowledge Graph</h2>
            <pre class="mermaid">
graph TD;

    %% Define all subgraphs and nodes first
    subgraph source_logic["Source & Logic"]
        subgraph main_py["main.py"]
            parse_args["parse_args()"]
            chunk_file["chunk_file()"]
            get_nlp["get_nlp()"]
            get_hf_ner_pipeline["get_hf_ner_pipeline()"]
            process_chunk_spacy["process_chunk_spacy()"]
            process_chunk_hf["process_chunk_hf()"]
            process_chunk["process_chunk()"]
            worker_thread["worker_thread()"]
            test_spacy_ner["test_spacy_ner()"]
            save_output["save_output()"]
            thread_local["thread_local"]
            main_execution["main_execution"]
        end
    end

    subgraph dependencies["Build & Dependencies"]
        requirements_txt["requirements.txt"]
        spacy_apple["spacy[apple]"]
        torch_lib["torch"]
        transformers_lib["transformers"]
        tree_sitter_lib["tree-sitter"]
    end

    subgraph data_config["Data & Configuration"]
        sample_txt["sample.txt"]
        spec_prompt_md["spec-prompt.md"]
        third_party_txt["ThirdPartyNoticeText.txt"]
        output_json["output.json"]
        output_dir["output/"]
    end

    subgraph external_models["External Models & APIs"]
        spacy_model["en_core_web_sm"]
        hf_model["dslim/bert-base-NER"]
        apple_gpu["Apple GPU (MPS)"]
    end

    %% Define all relationships
    main_execution --"calls"--> parse_args
    main_execution --"calls"--> chunk_file
    main_execution --"calls"--> worker_thread
    main_execution --"calls"--> process_chunk_hf
    main_execution --"calls"--> test_spacy_ner

    worker_thread --"calls"--> process_chunk
    process_chunk --"routes to"--> process_chunk_spacy
    process_chunk --"routes to"--> process_chunk_hf

    process_chunk_spacy --"calls"--> get_nlp
    process_chunk_hf --"calls"--> get_hf_ner_pipeline

    get_nlp --"loads"--> spacy_model
    get_hf_ner_pipeline --"loads"--> hf_model

    chunk_file --"reads"--> sample_txt
    main_execution --"writes"--> output_json
    main_execution --"writes"--> output_dir

    requirements_txt --"specifies"--> spacy_apple
    requirements_txt --"specifies"--> torch_lib
    requirements_txt --"specifies"--> transformers_lib
    requirements_txt --"specifies"--> tree_sitter_lib

    main_py --"depends on"--> requirements_txt
    spec_prompt_md --"specifies"--> main_py

    get_nlp --"uses"--> thread_local
    get_hf_ner_pipeline --"uses"--> thread_local

    torch_lib --"enables"--> apple_gpu
    spacy_apple --"optimized for"--> apple_gpu
    hf_model --"runs on"--> apple_gpu

    %% Styling
    classDef functionNode fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef dataNode fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef configNode fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef externalNode fill:#fff3e0,stroke:#e65100,stroke-width:2px

    class parse_args,chunk_file,get_nlp,get_hf_ner_pipeline,process_chunk_spacy,process_chunk_hf,process_chunk,worker_thread,test_spacy_ner,save_output,main_execution functionNode
    class sample_txt,output_json,output_dir,third_party_txt dataNode
    class requirements_txt,spec_prompt_md configNode
    class spacy_model,hf_model,apple_gpu,spacy_apple,torch_lib,transformers_lib,tree_sitter_lib externalNode
            </pre>
        </div>
        <div id="sidebar">
            <h2>Analysis & Refactoring</h2>
            <div id="analysis-report">
                <h3>🏗️ Architecture Overview</h3>
                <ul>
                    <li><strong>Type:</strong> Multi-threaded NLP processing application</li>
                    <li><strong>Primary Purpose:</strong> Entity and concept extraction with dual backend support</li>
                    <li><strong>Key Features:</strong> Apple Silicon optimization, thread-safe processing, configurable chunking</li>
                </ul>

                <h3>🔍 Key Strengths</h3>
                <ul>
                    <li><strong>Apple Silicon Optimization:</strong> Leverages Apple GPU through spacy[apple] and torch MPS backend</li>
                    <li><strong>Thread Safety:</strong> Proper use of threading.local() for model instances</li>
                    <li><strong>Flexible Processing:</strong> Configurable chunk sizes and worker threads for performance tuning</li>
                    <li><strong>Dual Backend Support:</strong> Both spaCy and HuggingFace transformers with unified output format</li>
                    <li><strong>Error Handling:</strong> Automatic model downloading and GPU availability checks</li>
                </ul>

                <h3>⚠️ Potential Issues</h3>
                <ul>
                    <li><strong>Unused Dependencies:</strong> tree-sitter libraries in requirements.txt are not used in main.py</li>
                    <li><strong>Hardcoded Values:</strong> Model names and entity labels are hardcoded</li>
                    <li><strong>Limited Error Handling:</strong> File I/O operations could benefit from more robust error handling</li>
                    <li><strong>Code Duplication:</strong> Similar spaCy loading logic in get_nlp() and test_spacy_ner()</li>
                </ul>

                <h3>🗂️ File Analysis</h3>
                <ul>
                    <li><strong>Active Files:</strong> main.py (232 lines), requirements.txt, sample.txt (3874 lines)</li>
                    <li><strong>Documentation:</strong> spec-prompt.md provides clear project requirements</li>
                    <li><strong>Generated Files:</strong> Multiple knowledge_graph*.html files (should be cleaned up)</li>
                    <li><strong>Output Structure:</strong> Well-organized with timestamped JSON outputs</li>
                </ul>

                <h3>🔄 Refactoring Suggestions</h3>
                <ul>
                    <li><strong>Configuration Management:</strong> Extract hardcoded values to a config file or environment variables</li>
                    <li><strong>Dependency Cleanup:</strong> Remove unused tree-sitter dependencies from requirements.txt</li>
                    <li><strong>Code Organization:</strong> Consider splitting into separate modules (processing, models, utils)</li>
                    <li><strong>Error Handling:</strong> Add comprehensive exception handling for file operations and model loading</li>
                    <li><strong>Testing:</strong> Add unit tests for individual functions, especially chunk processing logic</li>
                    <li><strong>Documentation:</strong> Add docstrings to all functions for better maintainability</li>
                </ul>

                <h3>📊 Metrics</h3>
                <ul>
                    <li><strong>Total Functions:</strong> 10 functions in main.py</li>
                    <li><strong>Threading Components:</strong> 1 worker function, 1 thread-local variable</li>
                    <li><strong>External Dependencies:</strong> 6 packages (4 actively used, 2 unused)</li>
                    <li><strong>Processing Backends:</strong> 2 (spaCy, HuggingFace)</li>
                    <li><strong>Code Complexity:</strong> Moderate - well-structured but could benefit from modularization</li>
                </ul>

                <h3>🎯 Next Steps</h3>
                <ul>
                    <li><strong>Immediate:</strong> Clean up unused knowledge_graph*.html files</li>
                    <li><strong>Short-term:</strong> Remove unused dependencies, add error handling</li>
                    <li><strong>Medium-term:</strong> Refactor into modules, add comprehensive testing</li>
                    <li><strong>Long-term:</strong> Consider adding more NLP backends, configuration management</li>
                </ul>
            </div>
        </div>
    </div>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
