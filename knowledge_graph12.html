<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Analysis Report</title>
    <style>
        body { font-family: sans-serif; display: flex; flex-direction: column; align-items: center; }
        #report-container { display: flex; width: 98%; gap: 16px; margin-top: 20px; }
        #graph-container { flex-grow: 1; border: 1px solid #ccc; padding: 10px; border-radius: 8px; }
        #sidebar { width: 400px; flex-shrink: 0; border: 1px solid #ccc; padding: 10px; border-radius: 8px; max-height: 80vh; overflow-y: auto; }
        h1, h2 { color: #333; }
        #analysis-report ul { list-style-type: none; padding-left: 0; }
        #analysis-report h3 { border-bottom: 1px solid #eee; padding-bottom: 5px; margin-top: 20px;}
        #analysis-report li { background-color: #f9f9f9; border: 1px solid #eee; padding: 8px; margin-bottom: 5px; border-radius: 4px; }
        #analysis-report strong { color: #c0392b; }
    </style>
</head>
<body>
    <h1>Codebase Analysis Report</h1>
    <div id="report-container">
        <div id="graph-container">
            <h2>Knowledge Graph</h2>
            <pre class="mermaid">
graph TD;
    subgraph "Source & Logic"
        direction TB
        main_py["main.py<br/>🐍 NLP Processing Engine"]
        parse_args["parse_args()<br/>CLI Parser"]
        chunk_file["chunk_file()<br/>Text Chunker"]
        get_nlp["get_nlp()<br/>spaCy Loader"]
        get_hf_ner["get_hf_ner_pipeline()<br/>HF Pipeline Loader"]
        process_chunk_spacy["process_chunk_spacy()<br/>spaCy Entity Extractor"]
        process_chunk_hf["process_chunk_hf()<br/>HF Entity Extractor"]
        worker_thread["worker_thread()<br/>Parallel Worker"]
        test_spacy_ner["test_spacy_ner()<br/>Pipeline Tester"]
        thread_local["thread_local<br/>🔒 Thread Storage"]
        
        main_py -- "contains" --> parse_args
        main_py -- "contains" --> chunk_file
        main_py -- "contains" --> get_nlp
        main_py -- "contains" --> get_hf_ner
        main_py -- "contains" --> process_chunk_spacy
        main_py -- "contains" --> process_chunk_hf
        main_py -- "contains" --> worker_thread
        main_py -- "contains" --> test_spacy_ner
        main_py -- "uses" --> thread_local
        
        get_nlp -- "loads" --> spacy_model["en_core_web_sm<br/>🧠 spaCy Model"]
        get_hf_ner -- "loads" --> bert_model["dslim/bert-base-NER<br/>🤖 BERT Model"]
        process_chunk_spacy -- "calls" --> get_nlp
        process_chunk_hf -- "calls" --> get_hf_ner
        worker_thread -- "executes" --> process_chunk_spacy
        worker_thread -- "executes" --> process_chunk_hf
    end

    subgraph "Build & Dependencies"
        direction TB
        requirements_txt["requirements.txt<br/>📦 Dependencies"]
        spacy_dep["spacy[apple]<br/>🍎 Apple Silicon"]
        torch_dep["torch<br/>⚡ ML Framework"]
        transformers_dep["transformers<br/>🤗 HuggingFace"]
        tree_sitter_dep["tree-sitter<br/>🌳 Parser"]
        
        requirements_txt -- "specifies" --> spacy_dep
        requirements_txt -- "specifies" --> torch_dep
        requirements_txt -- "specifies" --> transformers_dep
        requirements_txt -- "specifies" --> tree_sitter_dep
        
        main_py -- "imports" --> spacy_dep
        main_py -- "imports" --> torch_dep
        main_py -- "imports" --> transformers_dep
    end

    subgraph "Data & Configuration"
        direction TB
        sample_txt["sample.txt<br/>📄 AI Prompt Engineering Book"]
        third_party_txt["ThirdPartyNoticeText.txt<br/>⚖️ Legal Notices"]
        gitignore[".gitignore<br/>🚫 Git Exclusions"]
        output_dir["output/<br/>📁 Results Directory"]
        
        main_py -- "processes" --> sample_txt
        main_py -- "can process" --> third_party_txt
        main_py -- "generates" --> output_dir
    end

    subgraph "Documentation & Specification"
        direction TB
        spec_prompt_md["spec-prompt.md<br/>📋 Project Specification"]
        
        spec_prompt_md -- "defines requirements for" --> main_py
        spec_prompt_md -- "specifies dependencies in" --> requirements_txt
    end

    subgraph "Analysis & Outputs"
        direction TB
        code_inventory["code_inventory.json<br/>📊 Code Analysis"]
        knowledge_graph_html["knowledge_graph.html<br/>🕸️ This Report"]
        output_json["output.json<br/>💾 Extraction Results"]
        
        main_py -- "generates" --> output_json
        code_inventory -- "analyzes" --> main_py
        knowledge_graph_html -- "visualizes" --> code_inventory
    end

    %% Apple Silicon Optimization Highlights
    classDef appleOptimized fill:#ff9999,stroke:#333,stroke-width:2px,color:#000
    class spacy_dep,torch_dep,get_nlp,get_hf_ner appleOptimized

    %% Thread Safety Highlights  
    classDef threadSafe fill:#99ff99,stroke:#333,stroke-width:2px,color:#000
    class thread_local,worker_thread,get_nlp,get_hf_ner threadSafe

    %% Core Processing Highlights
    classDef coreLogic fill:#9999ff,stroke:#333,stroke-width:2px,color:#000
    class main_py,process_chunk_spacy,process_chunk_hf coreLogic
            </pre>
        </div>
        <div id="sidebar">
            <h2>Analysis & Refactoring</h2>
            <div id="analysis-report">
                <h3>🎯 Project Overview</h3>
                <ul>
                    <li><strong>Purpose:</strong> Apple Silicon optimized NLP entity/concept extraction using spaCy and HuggingFace transformers</li>
                    <li><strong>Architecture:</strong> Multi-threaded processing with configurable chunk sizes and worker threads</li>
                    <li><strong>Backends:</strong> Dual backend support (spaCy --sp, HuggingFace --hf) with command-line selection</li>
                </ul>

                <h3>🔍 Key Strengths</h3>
                <ul>
                    <li><strong>Apple Silicon Optimization:</strong> Leverages Apple GPU through spacy[apple] and torch MPS backend</li>
                    <li><strong>Thread Safety:</strong> Proper use of threading.local() for model instances</li>
                    <li><strong>Flexible Processing:</strong> Configurable chunk sizes and worker threads for performance tuning</li>
                    <li><strong>Dual Backend Support:</strong> Both spaCy and HuggingFace transformers with unified output format</li>
                    <li><strong>Error Handling:</strong> Automatic model downloading and GPU availability checks</li>
                </ul>

                <h3>⚠️ Potential Issues & Refactoring Suggestions</h3>
                <ul>
                    <li><strong>Code Duplication:</strong> process_chunk_spacy() and process_chunk_hf() have similar structure - consider abstract base class</li>
                    <li><strong>Configuration Management:</strong> Hard-coded model names and parameters could be externalized to config file</li>
                    <li><strong>Error Handling:</strong> Limited exception handling in worker threads could cause silent failures</li>
                    <li><strong>Memory Management:</strong> Large text files could cause memory issues without streaming processing</li>
                    <li><strong>Testing Coverage:</strong> Only basic spaCy test function - needs comprehensive test suite</li>
                </ul>

                <h3>🏗️ Architecture Insights</h3>
                <ul>
                    <li><strong>Processing Flow:</strong> File → Chunks → Workers → Extraction → Aggregation → Output</li>
                    <li><strong>Concurrency Model:</strong> Producer-consumer pattern with Queue for thread coordination</li>
                    <li><strong>Data Flow:</strong> Text input → Entity/Concept extraction → JSON serialization → Timestamped output</li>
                    <li><strong>Backend Abstraction:</strong> Clean separation between spaCy and HuggingFace processing logic</li>
                </ul>

                <h3>📊 File Analysis Summary</h3>
                <ul>
                    <li><strong>main.py:</strong> 232 lines, 10 functions, well-structured with clear separation of concerns</li>
                    <li><strong>requirements.txt:</strong> Apple Silicon optimized dependencies with specific versions</li>
                    <li><strong>sample.txt:</strong> 3,874 lines of AI prompt engineering content for testing</li>
                    <li><strong>spec-prompt.md:</strong> Comprehensive project specification with 22 detailed tasks</li>
                </ul>

                <h3>🚀 Recommended Next Steps</h3>
                <ul>
                    <li><strong>Add Unit Tests:</strong> Comprehensive test suite for all processing functions</li>
                    <li><strong>Configuration File:</strong> Externalize model names, parameters, and settings</li>
                    <li><strong>Logging Framework:</strong> Replace print statements with proper logging</li>
                    <li><strong>Performance Monitoring:</strong> Add metrics for processing speed and memory usage</li>
                    <li><strong>Error Recovery:</strong> Implement retry logic and graceful degradation</li>
                </ul>

                <h3>📈 Performance Characteristics</h3>
                <ul>
                    <li><strong>Scalability:</strong> Configurable workers (default 4) and chunk size (default 1000)</li>
                    <li><strong>Memory Efficiency:</strong> Streaming chunk processing prevents loading entire files</li>
                    <li><strong>GPU Utilization:</strong> Apple Metal Performance Shaders (MPS) backend for acceleration</li>
                    <li><strong>Thread Safety:</strong> Thread-local model instances prevent race conditions</li>
                </ul>
            </div>
        </div>
    </div>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.js';
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>
