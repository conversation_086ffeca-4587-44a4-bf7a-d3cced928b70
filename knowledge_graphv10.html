<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Analysis Report</title>
    <style>
        body { font-family: sans-serif; display: flex; flex-direction: column; align-items: center; }
        #report-container { display: flex; width: 98%; gap: 16px; margin-top: 20px; }
        #graph-container { flex-grow: 1; border: 1px solid #ccc; padding: 10px; border-radius: 8px; }
        #sidebar { width: 350px; flex-shrink: 0; border: 1px solid #ccc; padding: 10px; border-radius: 8px; }
        h1, h2 { color: #333; }
        #controls { margin-bottom: 15px; }
        #controls label { margin-right: 15px; }
        .mermaid .classNode { fill:#DDA0DD; stroke:#8A2BE2; } /* Plum for Classes */
        .mermaid .functionNode { fill:#87CEEB; stroke:#4682B4; } /* SkyBlue for Functions */
        .mermaid .dependencyNode { fill:#90EE90; stroke:#2E8B57; } /* LightGreen for Dependencies */
        #analysis-report ul { list-style-type: none; padding-left: 0; }
        #analysis-report li { background-color: #f9f9f9; border: 1px solid #eee; padding: 8px; margin-bottom: 5px; border-radius: 4px; }
        #analysis-report strong { color: #c0392b; }
    </style>
</head>
<body>
    <h1>Codebase Analysis Report</h1>
    <div id="report-container">
        <div id="graph-container">
            <h2>Knowledge Graph</h2>
            <div id="controls">
                <label><input type="checkbox" id="toggleFunctions" checked> Show Functions</label>
                <label><input type="checkbox" id="toggleClasses" checked> Show Classes</label>
                <label><input type="checkbox" id="toggleDependencies" checked> Show Dependencies</label>
            </div>
            <pre class="mermaid">
graph TD
    %% Dependencies
    spacy[spacy]:::dependencyNode
    torch[torch]:::dependencyNode
    transformers[transformers]:::dependencyNode
    tree_sitter[tree-sitter]:::dependencyNode
    tree_sitter_python[tree-sitter-python]:::dependencyNode
    tree_sitter_languages[tree-sitter-languages]:::dependencyNode
    
    %% Main Python File
    subgraph main_py["main.py"]
        parse_args[parse_args]:::functionNode
        chunk_file[chunk_file]:::functionNode
        get_nlp[get_nlp]:::functionNode
        get_hf_ner_pipeline[get_hf_ner_pipeline]:::functionNode
        process_chunk_spacy[process_chunk_spacy]:::functionNode
        process_chunk_hf[process_chunk_hf]:::functionNode
        process_chunk[process_chunk]:::functionNode
        worker_thread[worker_thread]:::functionNode
        test_spacy_ner[test_spacy_ner]:::functionNode
        save_output[save_output]:::functionNode
        thread_local[thread_local]:::functionNode
        main_execution[main_execution]:::functionNode
    end
    
    %% Requirements File
    subgraph requirements_txt["requirements.txt"]
        req_spacy[spacy[apple]]:::dependencyNode
        req_torch[torch]:::dependencyNode
        req_transformers[transformers]:::dependencyNode
        req_tree_sitter[tree-sitter==0.24.0]:::dependencyNode
        req_tree_sitter_python[tree-sitter-python]:::dependencyNode
        req_tree_sitter_languages[tree-sitter-languages]:::dependencyNode
    end
    
    %% Data Files
    subgraph data_files["Data Files"]
        sample_txt[sample.txt]:::functionNode
        third_party_txt[ThirdPartyNoticeText.txt]:::functionNode
        spec_prompt_md[spec-prompt.md]:::functionNode
    end
    
    %% Dependency relationships
    main_py --> spacy
    main_py --> torch
    main_py --> transformers
    
    %% Internal function relationships
    main_execution --> parse_args
    main_execution --> chunk_file
    main_execution --> worker_thread
    main_execution --> save_output
    main_execution --> test_spacy_ner
    
    worker_thread --> process_chunk
    process_chunk --> process_chunk_spacy
    process_chunk --> process_chunk_hf
    process_chunk_spacy --> get_nlp
    process_chunk_hf --> get_hf_ner_pipeline
    get_nlp --> thread_local
    get_hf_ner_pipeline --> thread_local
    test_spacy_ner --> get_nlp
    
    %% Data flow relationships
    chunk_file --> sample_txt
    chunk_file --> third_party_txt
    main_execution --> spec_prompt_md
    
    %% Requirements mapping
    req_spacy -.-> spacy
    req_torch -.-> torch
    req_transformers -.-> transformers
    req_tree_sitter -.-> tree_sitter
    req_tree_sitter_python -.-> tree_sitter_python
    req_tree_sitter_languages -.-> tree_sitter_languages
            </pre>
        </div>
        <div id="sidebar">
            <h2>Refactoring & Health Report</h2>
            <div id="analysis-report">
                <h3>📊 Code Health Summary</h3>
                <ul>
                    <li><strong>Total Files Analyzed:</strong> 5</li>
                    <li><strong>Python Source Files:</strong> 1</li>
                    <li><strong>Functions Identified:</strong> 11</li>
                    <li><strong>Dependencies:</strong> 6</li>
                    <li><strong>Last Analysis:</strong> 2025-07-02</li>
                </ul>
                
                <h3>🔍 Orphan Code Analysis</h3>
                <ul>
                    <li><strong>save_output function:</strong> Defined but appears to be unused in current codebase. Consider removing or integrating into main execution flow.</li>
                    <li><strong>tree-sitter dependencies:</strong> Listed in requirements.txt but not imported or used in main.py. These may be legacy dependencies that can be removed.</li>
                </ul>
                
                <h3>📈 Refactoring Opportunities</h3>
                <ul>
                    <li><strong>Code Duplication:</strong> spaCy model loading logic appears twice (get_nlp and test_spacy_ner). Consider consolidating into a single reusable function.</li>
                    <li><strong>Thread Safety:</strong> Both get_nlp and get_hf_ner_pipeline use thread_local storage. Consider creating a unified model manager class.</li>
                    <li><strong>Configuration Management:</strong> Command-line arguments are parsed but could benefit from a configuration class for better maintainability.</li>
                    <li><strong>Error Handling:</strong> Limited error handling throughout the codebase. Consider adding comprehensive exception handling.</li>
                </ul>
                
                <h3>🏗️ Architecture Suggestions</h3>
                <ul>
                    <li><strong>Separation of Concerns:</strong> Consider splitting main.py into separate modules: argument parsing, text processing, model management, and output handling.</li>
                    <li><strong>Abstract Base Classes:</strong> Create abstract processor interface to standardize spaCy and HuggingFace backends.</li>
                    <li><strong>Factory Pattern:</strong> Implement factory pattern for backend selection instead of conditional logic.</li>
                </ul>
                
                <h3>📋 Stale Files Analysis</h3>
                <ul>
                    <li><strong>No stale files detected:</strong> All analyzed files have been recently modified within the past year.</li>
                    <li><strong>Most Active File:</strong> main.py (9 commits)</li>
                    <li><strong>Configuration Files:</strong> requirements.txt and spec-prompt.md show regular updates</li>
                </ul>
                
                <h3>🎯 Priority Recommendations</h3>
                <ul>
                    <li><strong>High Priority:</strong> Remove unused tree-sitter dependencies to reduce package bloat</li>
                    <li><strong>Medium Priority:</strong> Refactor duplicate spaCy loading logic</li>
                    <li><strong>Low Priority:</strong> Consider architectural improvements for future scalability</li>
                </ul>
            </div>
        </div>
    </div>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.js';
        mermaid.initialize({ startOnLoad: true });

        // --- Interactive Filtering Logic ---
        function setupFilters() {
            const observer = new MutationObserver((mutations, obs) => {
                const svg = document.querySelector('#graph-container .mermaid svg');
                if (svg) {
                    addFilterListeners();
                    obs.disconnect(); // Stop observing once the SVG is found
                }
            });
            observer.observe(document.body, { childList: true, subtree: true });
        }
        
        function addFilterListeners() {
            document.getElementById('toggleFunctions').addEventListener('change', (e) => {
                toggleNodes('.functionNode', e.target.checked);
            });
            document.getElementById('toggleClasses').addEventListener('change', (e) => {
                toggleNodes('.classNode', e.target.checked);
            });
            document.getElementById('toggleDependencies').addEventListener('change', (e) => {
                toggleNodes('.dependencyNode', e.target.checked);
            });
        }

        function toggleNodes(selector, show) {
            const nodes = document.querySelectorAll(`.mermaid svg ${selector}`);
            nodes.forEach(node => {
                // Find the parent 'g' element which controls the node and its text
                const parentGroup = node.closest('.node');
                if(parentGroup) {
                    parentGroup.style.display = show ? '' : 'none';
                }
            });
        }

        setupFilters();
    </script>
</body>
</html>
