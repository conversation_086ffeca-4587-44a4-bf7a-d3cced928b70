{"metadata": {"timestamp": "2025-07-02_14-55-05", "analysis_version": "1.0", "repository_root": "/Users/<USER>/Dev-Space/pythonProjects/spacy-test", "total_files_analyzed": 6}, "files": {"main.py": {"type": "python_source", "size_lines": 232, "primary_purpose": "NLP entity and concept extraction using spaCy and HuggingFace", "imports": ["<PERSON><PERSON><PERSON><PERSON>", "threading", "queue.Queue", "spacy", "json", "os", "datetime.datetime", "torch", "transformers.pipeline"], "functions": [{"name": "parse_args", "line_start": 13, "line_end": 21, "purpose": "Command line argument parsing for chunk size, workers, input file, and backend selection"}, {"name": "chunk_file", "line_start": 23, "line_end": 32, "purpose": "File chunking generator for processing large text files"}, {"name": "get_nlp", "line_start": 36, "line_end": 44, "purpose": "Thread-safe spaCy model loader with automatic download"}, {"name": "get_hf_ner_pipeline", "line_start": 46, "line_end": 53, "purpose": "Thread-safe HuggingFace NER pipeline loader with Apple Silicon GPU support"}, {"name": "process_chunk_spacy", "line_start": 55, "line_end": 76, "purpose": "spaCy-based entity and concept extraction from text chunks"}, {"name": "process_chunk_hf", "line_start": 78, "line_end": 100, "purpose": "HuggingFace-based entity extraction with simple concept extraction"}, {"name": "process_chunk", "line_start": 102, "line_end": 106, "purpose": "Backend dispatcher for chunk processing"}, {"name": "worker_thread", "line_start": 108, "line_end": 117, "purpose": "Thread worker function for parallel processing"}, {"name": "test_spacy_ner", "line_start": 119, "line_end": 130, "purpose": "spaCy NER pipeline testing function"}, {"name": "save_output", "line_start": 132, "line_end": 138, "purpose": "Output saving utility function"}], "classes": [], "global_variables": [{"name": "thread_local", "line": 34, "purpose": "Thread-local storage for model instances"}], "main_execution": {"line_start": 140, "line_end": 232, "purpose": "Main execution flow with argument parsing, backend selection, and parallel processing"}}, "requirements.txt": {"type": "dependency_config", "size_lines": 10, "primary_purpose": "Python package dependencies for NLP processing", "dependencies": ["spacy[apple]", "torch", "transformers", "tree-sitter==0.24.0", "tree-sitter-python", "tree-sitter-languages"], "apple_silicon_optimized": true}, "sample.txt": {"type": "text_data", "size_lines": 3874, "primary_purpose": "Sample text data for AI prompt engineering book content", "content_type": "educational_text", "topics": ["AI prompt engineering", "Language models", "GPT and LLMs", "Training data", "Prompt strategies", "Creative applications"]}, "spec-prompt.md": {"type": "documentation", "size_lines": 61, "primary_purpose": "Project specification and requirements document", "sections": ["Instructions", "Name", "Objective", "Context", "Tasks", "Blocks"], "key_requirements": ["Apple Silicon optimization", "Multi-threaded processing", "spaCy and HuggingFace support", "Configurable chunk size and workers", "Entity and concept extraction"]}, ".gitignore": {"type": "git_config", "size_lines": 21, "primary_purpose": "Git ignore patterns for Python project", "ignored_patterns": ["*.pyc", "__pycache__/", "output/", ".env", ".DS_Store", ".venv", "knowledge_graph.html", "/codeanalysis/"]}, "ThirdPartyNoticeText.txt": {"type": "legal_document", "size_lines": 194, "primary_purpose": "Third-party license notices and legal text", "content_type": "legal_notices", "licenses_mentioned": ["MIT License", "Unicode License", "TypeScript ThirdPartyNotices"]}}}