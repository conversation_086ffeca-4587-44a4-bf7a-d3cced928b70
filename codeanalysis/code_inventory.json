{"metadata": {"timestamp": "2025-07-02T13:45:00Z", "analysis_version": "1.0"}, "files": {"main.py": {"type": "python_source", "description": "Main application file for spaCy/HuggingFace Apple Silicon NLP proof of concept", "functions": [{"name": "parse_args", "line_start": 13, "line_end": 21, "description": "Command line argument parser for chunk size, workers, input file, and backend selection"}, {"name": "chunk_file", "line_start": 23, "line_end": 32, "description": "Generator function to split input file into chunks of specified size"}, {"name": "get_nlp", "line_start": 36, "line_end": 44, "description": "Thread-safe spaCy model loader with automatic download fallback"}, {"name": "get_hf_ner_pipeline", "line_start": 46, "line_end": 53, "description": "Thread-safe HuggingFace NER pipeline loader with Apple Silicon GPU support"}, {"name": "process_chunk_spacy", "line_start": 55, "line_end": 76, "description": "Process text chunk using spaCy for entity and concept extraction"}, {"name": "process_chunk_hf", "line_start": 78, "line_end": 100, "description": "Process text chunk using HuggingFace transformers for entity extraction"}, {"name": "process_chunk", "line_start": 102, "line_end": 106, "description": "Backend dispatcher function to route chunk processing to spaCy or HuggingFace"}, {"name": "worker_thread", "line_start": 108, "line_end": 117, "description": "Worker thread function for parallel chunk processing"}, {"name": "test_spacy_ner", "line_start": 119, "line_end": 130, "description": "Test function to verify spaCy NER pipeline functionality"}, {"name": "save_output", "line_start": 132, "line_end": 138, "description": "Save extracted entities to timestamped JSON output file"}], "variables": [{"name": "thread_local", "line": 34, "description": "Thread-local storage for model instances"}], "imports": ["<PERSON><PERSON><PERSON><PERSON>", "threading", "queue.Queue", "spacy", "json", "os", "datetime.datetime", "torch", "transformers.pipeline"], "main_execution": {"line_start": 140, "line_end": 232, "description": "Main execution block with argument parsing, backend selection, parallel processing, and output generation"}}, "requirements.txt": {"type": "dependency_specification", "description": "Python package dependencies for the NLP processing application", "dependencies": [{"name": "spacy[apple]", "description": "spaCy NLP library with Apple Silicon optimizations"}, {"name": "torch", "description": "PyTorch deep learning framework with Metal backend support"}, {"name": "transformers", "description": "HuggingFace transformers library for pre-trained models"}, {"name": "tree-sitter", "version": "0.24.0", "description": "Tree-sitter parsing library"}, {"name": "tree-sitter-python", "description": "Python grammar for tree-sitter"}, {"name": "tree-sitter-languages", "description": "Language grammars collection for tree-sitter"}]}, "sample.txt": {"type": "text_data", "description": "Sample text file containing AI prompt engineering content for testing NLP extraction", "content_type": "educational_text", "topics": ["AI prompt engineering", "Language models", "Machine learning", "Content creation", "Business applications"]}, "spec-prompt.md": {"type": "specification_document", "description": "Project specification and task breakdown for spaCy NLP processor", "sections": [{"name": "Instructions", "description": "General instructions for following the specification"}, {"name": "Name", "description": "Project name: spacy NLP/ER processor - simple apple silicon proof of concept"}, {"name": "Objective", "description": "Requirements for entity/concept extractor with Apple Silicon optimization"}, {"name": "Context", "description": "Background information about building local graphRAG solution"}, {"name": "Tasks", "description": "22 numbered tasks from Git initialization to HuggingFace integration"}]}, "ThirdPartyNoticeText.txt": {"type": "legal_document", "description": "Third-party license notices and copyright information", "content_type": "legal_notices", "sections": ["TypeScript ThirdPartyNotices", "DefinitelyTyped MIT License", "Unicode License Agreement"]}}}