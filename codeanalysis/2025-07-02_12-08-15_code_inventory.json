{"metadata": {"timestamp": "2025-07-02_12-08-15", "commit_hash": "60ae6fab647e542e70c6f6d65f7a89ed218e43b8"}, "files": {"main.py": {"type": "SourceFile", "entities": [{"name": "parse_args", "type": "Function"}, {"name": "chunk_file", "type": "Function"}, {"name": "get_nlp", "type": "Function"}, {"name": "get_hf_ner_pipeline", "type": "Function"}, {"name": "process_chunk_spacy", "type": "Function"}, {"name": "process_chunk_hf", "type": "Function"}, {"name": "process_chunk", "type": "Function"}, {"name": "worker_thread", "type": "Function"}, {"name": "test_spacy_ner", "type": "Function"}, {"name": "save_output", "type": "Function"}, {"name": "thread_local", "type": "Variable"}]}, "build_language_library.py": {"type": "SourceFile", "entities": [{"name": "GRAMMARS", "type": "Variable"}, {"name": "LIB_PATH", "type": "Variable"}]}, "generate_knowledge_graph.py": {"type": "SourceFile", "entities": [{"name": "setup_treesitter_grammars", "type": "Function"}, {"name": "get_gitignore", "type": "Function"}, {"name": "categorize_file", "type": "Function"}, {"name": "add_file_node", "type": "Function"}, {"name": "parse_requirements", "type": "Function"}, {"name": "parse_python_code", "type": "Function"}, {"name": "main", "type": "Function"}]}, "lib/bindings/utils.js": {"type": "SourceFile", "entities": [{"name": "neighbourhoodHighlight", "type": "Function"}, {"name": "filterHighlight", "type": "Function"}, {"name": "selectNode", "type": "Function"}, {"name": "selectNodes", "type": "Function"}, {"name": "highlightFilter", "type": "Function"}]}, "requirements.txt": {"type": "BuildFile", "entities": [{"name": "spacy[apple]", "type": "Dependency"}, {"name": "torch", "type": "Dependency"}, {"name": "transformers", "type": "Dependency"}, {"name": "tree-sitter", "type": "Dependency"}, {"name": "tree-sitter-python", "type": "Dependency"}, {"name": "tree-sitter-languages", "type": "Dependency"}]}, "spec-prompt.md": {"type": "DocumentationFile", "entities": [{"name": "Instructions", "type": "Section"}, {"name": "Name", "type": "Section"}, {"name": "Objective", "type": "Section"}, {"name": "Context", "type": "Section"}, {"name": "Tasks", "type": "Section"}, {"name": "Blocks", "type": "Section"}]}, "ThirdPartyNoticeText.txt": {"type": "DocumentationFile", "entities": []}, ".gitignore": {"type": "ConfigFile", "entities": []}, "output.json": {"type": "ConfigFile", "entities": []}, "sample.txt": {"type": "OtherFile", "entities": []}, "lib/tom-select/tom-select.complete.min.js": {"type": "SourceFile", "entities": []}, "lib/tom-select/tom-select.css": {"type": "SourceFile", "entities": []}, "lib/vis-9.1.2/vis-network.css": {"type": "SourceFile", "entities": []}, "lib/vis-9.1.2/vis-network.min.js": {"type": "SourceFile", "entities": []}, ".plandex-v2/projects-v2.json": {"type": "ConfigFile", "entities": []}}}