{"metadata": {"timestamp": "2025-07-02T15:30:00Z", "analysis_version": "1.0", "repository_root": "/Users/<USER>/Dev-Space/pythonProjects/spacy-test", "total_files_analyzed": 5, "analysis_type": "comprehensive_code_inventory"}, "files": {"main.py": {"type": "python_source", "size_lines": 232, "purpose": "Main NLP/NER processing application with dual backend support (spaCy/HuggingFace)", "functions": [{"name": "parse_args", "line_start": 13, "line_end": 21, "parameters": [], "returns": "argparse.Namespace", "purpose": "Command line argument parsing for chunk size, workers, input file, and backend selection", "calls": ["argparse.ArgumentParser", "parser.add_argument", "parser.add_mutually_exclusive_group", "parser.parse_args"]}, {"name": "chunk_file", "line_start": 23, "line_end": 32, "parameters": ["filepath", "chunk_size"], "returns": "generator", "purpose": "File chunking generator for processing large text files", "calls": ["open", "sum", "len", "join"]}, {"name": "get_nlp", "line_start": 36, "line_end": 44, "parameters": [], "returns": "spacy.Language", "purpose": "Thread-safe spaCy model loader with automatic download", "calls": ["has<PERSON>r", "spacy.load", "spacy.cli.download.download"]}, {"name": "get_hf_ner_pipeline", "line_start": 46, "line_end": 53, "parameters": ["global_hf_ner"], "returns": "transformers.Pipeline", "purpose": "Thread-safe HuggingFace NER pipeline loader with Apple Silicon GPU support", "calls": ["has<PERSON>r", "torch.backends.mps.is_available", "pipeline"]}, {"name": "process_chunk_spacy", "line_start": 55, "line_end": 76, "parameters": ["chunk", "chunk_id"], "returns": "dict", "purpose": "spaCy-based entity and concept extraction from text chunks", "calls": ["get_nlp", "print", "len"]}, {"name": "process_chunk_hf", "line_start": 78, "line_end": 100, "parameters": ["chunk", "chunk_id", "global_hf_ner"], "returns": "dict", "purpose": "HuggingFace-based entity extraction with simple concept extraction", "calls": ["get_hf_ner_pipeline", "Counter", "re.findall", "print", "len"]}, {"name": "process_chunk", "line_start": 102, "line_end": 106, "parameters": ["chunk", "chunk_id", "backend"], "returns": "dict", "purpose": "Backend dispatcher for chunk processing", "calls": ["process_chunk_spacy", "process_chunk_hf"]}, {"name": "worker_thread", "line_start": 108, "line_end": 117, "parameters": ["q", "results", "results_lock", "backend"], "returns": "None", "purpose": "Thread worker function for parallel processing", "calls": ["q.get", "process_chunk", "results.append", "q.task_done"]}, {"name": "test_spacy_ner", "line_start": 119, "line_end": 130, "parameters": [], "returns": "None", "purpose": "spaCy NER pipeline testing function", "calls": ["print", "spacy.load", "spacy.cli.download.download"]}, {"name": "save_output", "line_start": 132, "line_end": 138, "parameters": ["entities"], "returns": "None", "purpose": "Output saving utility function", "calls": ["os.makedirs", "datetime.now", "open", "json.dump", "print"]}], "classes": [], "global_variables": [{"name": "thread_local", "line": 34, "type": "threading.local", "purpose": "Thread-local storage for model instances"}], "imports": [{"module": "<PERSON><PERSON><PERSON><PERSON>", "line": 1}, {"module": "threading", "line": 2}, {"module": "queue", "items": ["Queue"], "line": 3}, {"module": "spacy", "line": 4}, {"module": "json", "line": 5}, {"module": "os", "line": 6}, {"module": "datetime", "items": ["datetime"], "line": 7}, {"module": "torch", "line": 8}, {"module": "transformers", "items": ["pipeline"], "line": 9}], "main_execution": {"line_start": 140, "line_end": 232, "description": "Main execution block with argument parsing, backend selection, parallel processing, and output generation"}}, "requirements.txt": {"type": "dependency_file", "size_lines": 10, "purpose": "Python package dependencies with Apple Silicon optimizations", "dependencies": [{"name": "spacy[apple]", "purpose": "Apple Silicon optimized spaCy"}, {"name": "torch", "purpose": "PyTorch with MPS backend support"}, {"name": "transformers", "purpose": "HuggingFace transformers library"}, {"name": "tree-sitter", "version": "0.24.0", "purpose": "Code parsing library"}, {"name": "tree-sitter-python", "purpose": "Python language support for tree-sitter"}, {"name": "tree-sitter-languages", "purpose": "Multiple language support for tree-sitter"}]}, "sample.txt": {"type": "data_file", "size_lines": 3874, "purpose": "Sample text data for NLP processing - AI prompt engineering book content", "content_type": "text/plain"}, "spec-prompt.md": {"type": "documentation", "size_lines": 61, "purpose": "Project specification and requirements document", "sections": [{"name": "Instructions", "lines": "1-7"}, {"name": "Name", "lines": "10-12"}, {"name": "Objective", "lines": "14-22"}, {"name": "Context", "lines": "24-29"}, {"name": "Tasks", "lines": "31-61"}]}, "ThirdPartyNoticeText.txt": {"type": "legal_document", "size_lines": "unknown", "purpose": "Third-party license notices and legal text", "content_type": "text/plain"}}, "relationships": {"function_calls": [{"caller": "main_execution", "callee": "parse_args", "type": "CALLS"}, {"caller": "main_execution", "callee": "chunk_file", "type": "CALLS"}, {"caller": "main_execution", "callee": "worker_thread", "type": "CALLS"}, {"caller": "main_execution", "callee": "process_chunk_hf", "type": "CALLS"}, {"caller": "main_execution", "callee": "test_spacy_ner", "type": "CALLS"}, {"caller": "worker_thread", "callee": "process_chunk", "type": "CALLS"}, {"caller": "process_chunk", "callee": "process_chunk_spacy", "type": "CALLS"}, {"caller": "process_chunk", "callee": "process_chunk_hf", "type": "CALLS"}, {"caller": "process_chunk_spacy", "callee": "get_nlp", "type": "CALLS"}, {"caller": "process_chunk_hf", "callee": "get_hf_ner_pipeline", "type": "CALLS"}, {"caller": "get_nlp", "callee": "spacy.load", "type": "CALLS"}, {"caller": "get_hf_ner_pipeline", "callee": "pipeline", "type": "CALLS"}], "data_dependencies": [{"source": "main.py", "target": "sample.txt", "type": "READS"}, {"source": "main.py", "target": "requirements.txt", "type": "DEPENDS_ON"}, {"source": "spec-prompt.md", "target": "main.py", "type": "SPECIFIES"}], "threading_relationships": [{"component": "worker_thread", "synchronization": "Queue", "type": "PRODUCER_CONSUMER"}, {"component": "worker_thread", "synchronization": "threading.Lock", "type": "MUTEX"}, {"component": "thread_local", "scope": "per_thread", "type": "THREAD_LOCAL_STORAGE"}]}}