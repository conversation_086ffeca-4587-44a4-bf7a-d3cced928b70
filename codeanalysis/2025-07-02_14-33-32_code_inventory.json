{"metadata": {"timestamp": "2025-07-02_14-33-32", "commit_hash": "1a78242b5f8c5d282d1975779b381079ad5bfea2"}, "files": {"knowledge_graphv10.html": {"type": "DocumentationFile", "entities": []}, ".agentic-tools-mcp/tasks/tasks.json": {"type": "ConfigFile", "entities": [{"name": "projects", "type": "Property"}, {"name": "tasks", "type": "Property"}, {"name": "subtasks", "type": "Property"}]}, ".gitignore": {"type": "ConfigFile", "entities": []}, "ThirdPartyNoticeText.txt": {"type": "DocumentationFile", "entities": []}, "codeanalysis/2025-07-02_12-08-15_code_inventory.json": {"type": "ConfigFile", "entities": []}, "codeanalysis/2025-07-02_12-18-06_code_inventory.json": {"type": "ConfigFile", "entities": []}, "main.py": {"type": "SourceFile", "entities": [{"name": "parse_args", "type": "Function"}, {"name": "chunk_file", "type": "Function"}, {"name": "get_nlp", "type": "Function"}, {"name": "get_hf_ner_pipeline", "type": "Function"}, {"name": "process_chunk_spacy", "type": "Function"}, {"name": "process_chunk_hf", "type": "Function"}, {"name": "process_chunk", "type": "Function"}, {"name": "worker_thread", "type": "Function"}, {"name": "test_spacy_ner", "type": "Function"}, {"name": "save_output", "type": "Function"}, {"name": "thread_local", "type": "Variable"}]}, "output.json": {"type": "ConfigFile", "entities": [{"name": "input_file", "type": "Property"}, {"name": "chunk_size", "type": "Property"}, {"name": "worker_count", "type": "Property"}, {"name": "total_chunks", "type": "Property"}, {"name": "total_entities", "type": "Property"}, {"name": "total_concepts", "type": "Property"}, {"name": "entities", "type": "Property"}, {"name": "concepts", "type": "Property"}]}, "requirements.txt": {"type": "BuildFile", "entities": [{"name": "spacy[apple]", "type": "Dependency"}, {"name": "torch", "type": "Dependency"}, {"name": "transformers", "type": "Dependency"}, {"name": "tree-sitter", "type": "Dependency"}, {"name": "tree-sitter-python", "type": "Dependency"}, {"name": "tree-sitter-languages", "type": "Dependency"}]}, "sample.txt": {"type": "DocumentationFile", "entities": []}, "spec-prompt.md": {"type": "DocumentationFile", "entities": []}}}