{"metadata": {"timestamp": "2025-07-02_13-44-56", "commit_hash": "028a1e865e8a569ac890dc2334b1ab47febe6f00"}, "files": {"spec-prompt.md": {"type": "DocumentationFile", "entities": []}, "requirements.txt": {"type": "BuildFile", "entities": [{"name": "spacy", "type": "Dependency"}, {"name": "torch", "type": "Dependency"}, {"name": "transformers", "type": "Dependency"}, {"name": "tree-sitter", "type": "Dependency"}, {"name": "tree-sitter-python", "type": "Dependency"}, {"name": "tree-sitter-languages", "type": "Dependency"}]}, "output.json": {"type": "ConfigFile", "entities": []}, "main.py": {"type": "SourceFile", "entities": [{"name": "parse_args", "type": "Function"}, {"name": "chunk_file", "type": "Function"}, {"name": "get_nlp", "type": "Function"}, {"name": "get_hf_ner_pipeline", "type": "Function"}, {"name": "process_chunk_spacy", "type": "Function"}, {"name": "process_chunk_hf", "type": "Function"}, {"name": "process_chunk", "type": "Function"}, {"name": "worker_thread", "type": "Function"}, {"name": "test_spacy_ner", "type": "Function"}, {"name": "save_output", "type": "Function"}]}, "ThirdPartyNoticeText.txt": {"type": "DocumentationFile", "entities": []}, "sample.txt": {"type": "DocumentationFile", "entities": []}, "lib/tom-select/tom-select.complete.min.js": {"type": "SourceFile", "entities": [{"name": "e", "type": "Function"}, {"name": "U", "type": "Function"}, {"name": "that", "type": "Function"}, {"name": "o", "type": "Function"}, {"name": "l", "type": "Function"}, {"name": "c", "type": "Function"}, {"name": "m", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "s", "type": "Function"}, {"name": "tomSelect", "type": "Function"}]}, "lib/tom-select/tom-select.css": {"type": "SourceFile", "entities": []}, "lib/vis-9.1.2/vis-network.min.js": {"type": "SourceFile", "entities": [{"name": "Nn", "type": "Function"}, {"name": "Fn", "type": "Function"}, {"name": "An", "type": "Function"}, {"name": "jn", "type": "Function"}, {"name": "Rn", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "Yd", "type": "Function"}, {"name": "Gd", "type": "Function"}, {"name": "Kd", "type": "Function"}, {"name": "Xc", "type": "Function"}, {"name": "Gc", "type": "Function"}, {"name": "Kc", "type": "Function"}, {"name": "Qc", "type": "Function"}, {"name": "Jc", "type": "Function"}, {"name": "Rv", "type": "Function"}, {"name": "Lv", "type": "Function"}, {"name": "Hv", "type": "Function"}, {"name": "Kv", "type": "Function"}, {"name": "pg", "type": "Function"}, {"name": "vg", "type": "Function"}, {"name": "gg", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "mg", "type": "Function"}, {"name": "bg", "type": "Function"}, {"name": "wg", "type": "Function"}, {"name": "kg", "type": "Function"}, {"name": "_g", "type": "Function"}, {"name": "xg", "type": "Function"}, {"name": "Eg", "type": "Function"}, {"name": "Og", "type": "Function"}, {"name": "Cg", "type": "Function"}, {"name": "Sg", "type": "Function"}, {"name": "Tg", "type": "Function"}, {"name": "Mg", "type": "Function"}, {"name": "Pg", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "Ig", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "jg", "type": "Function"}, {"name": "Rg", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "qg", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "Gg", "type": "Function"}, {"name": "Kg", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "Qg", "type": "Function"}, {"name": "ey", "type": "Function"}, {"name": "iy", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "n", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "sy", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "py", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "wy", "type": "Function"}, {"name": "ky", "type": "Function"}, {"name": "<PERSON><PERSON>", "type": "Function"}, {"name": "Oy", "type": "Function"}, {"name": "Ty", "type": "Function"}, {"name": "My", "type": "Function"}, {"name": "P<PERSON>", "type": "Function"}, {"name": "<PERSON><PERSON>", "type": "Function"}, {"name": "By", "type": "Function"}, {"name": "zy", "type": "Function"}, {"name": "Ny", "type": "Function"}, {"name": "Fy", "type": "Function"}, {"name": "Ay", "type": "Function"}, {"name": "jy", "type": "Function"}, {"name": "<PERSON><PERSON>", "type": "Function"}, {"name": "Ly", "type": "Function"}, {"name": "Wy", "type": "Function"}, {"name": "Gy", "type": "Function"}, {"name": "K<PERSON>", "type": "Function"}, {"name": "<PERSON><PERSON>", "type": "Function"}, {"name": "Qy", "type": "Function"}, {"name": "<PERSON><PERSON>", "type": "Function"}, {"name": "em", "type": "Function"}, {"name": "im", "type": "Function"}, {"name": "nm", "type": "Function"}, {"name": "om", "type": "Function"}, {"name": "rm", "type": "Function"}, {"name": "sm", "type": "Function"}, {"name": "am", "type": "Function"}, {"name": "hm", "type": "Function"}, {"name": "dm", "type": "Function"}, {"name": "cm", "type": "Function"}, {"name": "fm", "type": "Function"}, {"name": "pm", "type": "Function"}, {"name": "vm", "type": "Function"}, {"name": "gm", "type": "Function"}, {"name": "ym", "type": "Function"}, {"name": "wm", "type": "Function"}, {"name": "km", "type": "Function"}, {"name": "_m", "type": "Function"}, {"name": "xm", "type": "Function"}, {"name": "Em", "type": "Function"}, {"name": "Om", "type": "Function"}, {"name": "Cm", "type": "Function"}, {"name": "Sm", "type": "Function"}, {"name": "Mm", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "Im", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "Xm", "type": "Function"}, {"name": "sb", "type": "Function"}, {"name": "ab", "type": "Function"}, {"name": "hb", "type": "Function"}, {"name": "lb", "type": "Function"}, {"name": "db", "type": "Function"}, {"name": "cb", "type": "Function"}, {"name": "ub", "type": "Function"}, {"name": "fb", "type": "Function"}, {"name": "pb", "type": "Function"}, {"name": "vb", "type": "Function"}, {"name": "gb", "type": "Function"}, {"name": "yb", "type": "Function"}, {"name": "mb", "type": "Function"}, {"name": "bb", "type": "Function"}, {"name": "wb", "type": "Function"}, {"name": "kb", "type": "Function"}, {"name": "_b", "type": "Function"}, {"name": "xb", "type": "Function"}, {"name": "Eb", "type": "Function"}, {"name": "Cb", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "Pk", "type": "Function"}, {"name": "Bk", "type": "Function"}, {"name": "zk", "type": "Function"}, {"name": "Nk", "type": "Function"}, {"name": "Ak", "type": "Function"}, {"name": "h", "type": "Function"}, {"name": "l", "type": "Function"}, {"name": "d", "type": "Function"}, {"name": "g", "type": "Function"}, {"name": "y", "type": "Function"}, {"name": "m", "type": "Function"}, {"name": "x", "type": "Function"}, {"name": "E", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "r", "type": "Function"}, {"name": "O", "type": "Function"}, {"name": "C", "type": "Function"}, {"name": "S", "type": "Function"}, {"name": "T", "type": "Function"}, {"name": "M", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "P", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "o", "type": "Function"}, {"name": "Dx", "type": "Function"}, {"name": "Bx", "type": "Function"}, {"name": "Ax", "type": "Function"}, {"name": "jx", "type": "Function"}, {"name": "Rx", "type": "Function"}, {"name": "Lx", "type": "Function"}, {"name": "Hx", "type": "Function"}, {"name": "Wx", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "Ux", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "or", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "Zx", "type": "Function"}, {"name": "Qx", "type": "Function"}, {"name": "gE", "type": "Function"}, {"name": "yE", "type": "Function"}, {"name": "mE", "type": "Function"}, {"name": "bE", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "SE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "ME", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "DE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "BE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "NE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "AE", "type": "Function"}, {"name": "jE", "type": "Function"}, {"name": "RE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "HE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "qE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "UE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "XE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "KE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "ZE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "JE", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "eO", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "nO", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "rO", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "aO", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "lO", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "cO", "type": "Function"}, {"name": "uO", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "pO", "type": "Function"}, {"name": "vO", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "CO", "type": "Function"}, {"name": "SO", "type": "Function"}, {"name": "BO", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "GO", "type": "Function"}, {"name": "KO", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "ZO", "type": "Function"}, {"name": "QO", "type": "Function"}, {"name": "JO", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "eC", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "nC", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "rC", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "aC", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "lC", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "bC", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "kC", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "OC", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "in", "type": "Function"}, {"name": "does", "type": "Function"}, {"name": "TC", "type": "Function"}, {"name": "MC", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "IC", "type": "Function"}, {"name": "BC", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "NC", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "AC", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "LC", "type": "Function"}, {"name": "HC", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "IS", "type": "Function"}, {"name": "BS", "type": "Function"}, {"name": "zS", "type": "Function"}, {"name": "NS", "type": "Function"}, {"name": "FS", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "RS", "type": "Function"}, {"name": "LS", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "s", "type": "Function"}, {"name": "a", "type": "Function"}, {"name": "h", "type": "Function"}, {"name": "l", "type": "Function"}, {"name": "d", "type": "Function"}, {"name": "c", "type": "Function"}, {"name": "u", "type": "Function"}, {"name": "f", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "v", "type": "Function"}, {"name": "VS", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "eT", "type": "Function"}, {"name": "iT", "type": "Function"}, {"name": "nT", "type": "Function"}, {"name": "oT", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "aT", "type": "Function"}, {"name": "hT", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "has", "type": "Function"}, {"name": "for", "type": "Function"}, {"name": "for", "type": "Function"}, {"name": "does", "type": "Function"}, {"name": "for", "type": "Function"}, {"name": "for", "type": "Function"}, {"name": "for", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "xT", "type": "Function"}, {"name": "ET", "type": "Function"}, {"name": "OT", "type": "Function"}, {"name": "CT", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "Na", "type": "Function"}, {"name": "vv", "type": "Function"}, {"name": "yg", "type": "Function"}, {"name": "Dg", "type": "Function"}, {"name": "Ag", "type": "Function"}, {"name": "Zg", "type": "Function"}, {"name": "ny", "type": "Function"}, {"name": "ay", "type": "Function"}, {"name": "vy", "type": "Function"}, {"name": "Cy", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "t", "type": "Function"}, {"name": "mm", "type": "Function"}, {"name": "a", "type": "Function"}, {"name": "a", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "Mb", "type": "Function"}, {"name": "d", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "qx", "type": "Function"}, {"name": "Yx", "type": "Function"}, {"name": "Gx", "type": "Function"}, {"name": "Kx", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "wE", "type": "Function"}, {"name": "n", "type": "Function"}, {"name": "o", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "TE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "PE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "IE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "zE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "FE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "LE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "WE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "VE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "YE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "GE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "QE", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "tO", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "iO", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "oO", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "sO", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "hO", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "dO", "type": "Function"}, {"name": "fO", "type": "Function"}, {"name": "gO", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "zO", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "tC", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "iC", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "oC", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "sC", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "hC", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "dC", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "wC", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "_C", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "CC", "type": "Function"}, {"name": "p", "type": "Function"}, {"name": "m", "type": "Function"}, {"name": "PC", "type": "Function"}, {"name": "zC", "type": "Function"}, {"name": "FC", "type": "Function"}, {"name": "a", "type": "Function"}, {"name": "WC", "type": "Function"}, {"name": "i", "type": "Function"}, {"name": "AS", "type": "Function"}, {"name": "jS", "type": "Function"}, {"name": "HS", "type": "Function"}, {"name": "p", "type": "Function"}, {"name": "e", "type": "Function"}, {"name": "US", "type": "Function"}, {"name": "c", "type": "Function"}, {"name": "rT", "type": "Function"}, {"name": "g", "type": "Function"}, {"name": "lT", "type": "Function"}, {"name": "n", "type": "Function"}]}, "lib/vis-9.1.2/vis-network.css": {"type": "SourceFile", "entities": []}, "lib/bindings/utils.js": {"type": "SourceFile", "entities": [{"name": "neighbourhoodHighlight", "type": "Function"}, {"name": "filterHighlight", "type": "Function"}, {"name": "selectNode", "type": "Function"}, {"name": "selectNodes", "type": "Function"}, {"name": "highlightFilter", "type": "Function"}]}}}