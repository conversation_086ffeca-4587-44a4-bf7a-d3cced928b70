<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Analysis Report</title>
    <style>
        body { font-family: sans-serif; display: flex; flex-direction: column; align-items: center; }
        #report-container { display: flex; width: 98%; gap: 16px; margin-top: 20px; }
        #graph-container { flex-grow: 1; border: 1px solid #ccc; padding: 10px; border-radius: 8px; }
        #sidebar { width: 400px; flex-shrink: 0; border: 1px solid #ccc; padding: 10px; border-radius: 8px; max-height: 85vh; overflow-y: auto; }
        h1, h2 { color: #333; }
        #analysis-report ul { list-style-type: none; padding-left: 0; }
        #analysis-report h3 { border-bottom: 1px solid #eee; padding-bottom: 5px; margin-top: 20px;}
        #analysis-report li { background-color: #f9f9f9; border: 1px solid #eee; padding: 8px; margin-bottom: 5px; border-radius: 4px; }
        #analysis-report strong { color: #c0392b; }
        /* Mermaid Node Styling */
        .classNode { fill:#DDA0DD; stroke:#8A2BE2; stroke-width:2px; }
        .functionNode { fill:#87CEEB; stroke:#4682B4; stroke-width:2px; }
        .dependencyNode { fill:#90EE90; stroke:#2E8B57; stroke-width:2px; }
        .fileNode { fill:#FFFACD; stroke:#FFD700; stroke-width:2px; }
    </style>
</head>
<body>
    <h1>Codebase Analysis Report</h1>
    <div id="report-container">
        <div id="graph-container">
            <h2>Knowledge Graph</h2>
            <pre class="mermaid">
graph TD;

    %% Define all subgraphs and nodes first
    subgraph source_logic["Source & Logic"]
        subgraph main_py_module["main.py"]
            parse_args("parse_args()"):::functionNode
            chunk_file("chunk_file()"):::functionNode
            get_nlp("get_nlp()"):::functionNode
            get_hf_ner_pipeline("get_hf_ner_pipeline()"):::functionNode
            process_chunk_spacy("process_chunk_spacy()"):::functionNode
            process_chunk_hf("process_chunk_hf()"):::functionNode
            process_chunk("process_chunk()"):::functionNode
            worker_thread("worker_thread()"):::functionNode
            test_spacy_ner("test_spacy_ner()"):::functionNode
            save_output("save_output()"):::functionNode
            thread_local_var("thread_local"):::classNode
            main_execution("main_execution"):::functionNode
        end
    end

    subgraph dependencies["Build & Dependencies"]
        requirements_txt("requirements.txt"):::fileNode
        spacy_apple("spacy[apple]"):::dependencyNode
        torch_lib("torch"):::dependencyNode
        transformers_lib("transformers"):::dependencyNode
        tree_sitter_lib("tree-sitter"):::dependencyNode
        tree_sitter_python("tree-sitter-python"):::dependencyNode
        tree_sitter_languages("tree-sitter-languages"):::dependencyNode
    end

    subgraph data_config["Data & Configuration"]
        sample_txt("sample.txt"):::fileNode
        spec_prompt_md("spec-prompt.md"):::fileNode
        third_party_txt("ThirdPartyNoticeText.txt"):::fileNode
        output_json("output.json"):::fileNode
        output_dir("output/"):::fileNode
    end

    subgraph external_models["External Models & APIs"]
        spacy_model("en_core_web_sm"):::dependencyNode
        hf_model("dslim/bert-base-NER"):::dependencyNode
        apple_gpu("Apple GPU (MPS)"):::dependencyNode
    end

    %% Define all relationships last
    main_execution --"calls"--> parse_args
    main_execution --"calls"--> chunk_file
    main_execution --"calls"--> process_chunk_hf
    main_execution --"calls"--> test_spacy_ner
    main_execution --"reads"--> sample_txt
    main_execution --"writes"--> output_json
    main_execution --"writes"--> output_dir

    worker_thread --"calls"--> process_chunk
    process_chunk --"routes_to"--> process_chunk_spacy
    process_chunk --"routes_to"--> process_chunk_hf
    process_chunk_spacy --"calls"--> get_nlp
    process_chunk_hf --"calls"--> get_hf_ner_pipeline

    get_nlp --"uses"--> thread_local_var
    get_hf_ner_pipeline --"uses"--> thread_local_var
    get_nlp --"loads"--> spacy_model
    get_hf_ner_pipeline --"loads"--> hf_model
    get_hf_ner_pipeline --"uses"--> apple_gpu

    requirements_txt --"specifies"--> spacy_apple
    requirements_txt --"specifies"--> torch_lib
    requirements_txt --"specifies"--> transformers_lib
    requirements_txt --"specifies"--> tree_sitter_lib
    requirements_txt --"specifies"--> tree_sitter_python
    requirements_txt --"specifies"--> tree_sitter_languages

    main_py_module --"depends_on"--> spacy_apple
    main_py_module --"depends_on"--> torch_lib
    main_py_module --"depends_on"--> transformers_lib

    save_output --"writes"--> output_dir
            </pre>
        </div>
        <div id="sidebar">
            <h2>Analysis & Refactoring</h2>
            <div id="analysis-report">
                <h3>🏗️ Architecture Overview</h3>
                <ul>
                    <li><strong>Type:</strong> Multi-threaded NLP processing application</li>
                    <li><strong>Primary Purpose:</strong> Entity and concept extraction with dual backend support</li>
                    <li><strong>Backends:</strong> spaCy (with Apple Silicon GPU) and HuggingFace Transformers</li>
                    <li><strong>Threading Model:</strong> Queue-based worker threads with thread-local model storage</li>
                    <li><strong>Apple Silicon Optimized:</strong> Uses MPS backend and spacy[apple] package</li>
                </ul>

                <h3>🔍 Orphan Code Detection</h3>
                <ul>
                    <li><strong>Unused Dependencies:</strong> tree-sitter, tree-sitter-python, tree-sitter-languages are specified in requirements.txt but never imported or used in main.py</li>
                    <li><strong>Unused Function:</strong> save_output() function is defined but never called in the main execution flow</li>
                    <li><strong>Dead Code:</strong> The save_output function creates timestamped files but main execution uses inline file saving instead</li>
                </ul>

                <h3>📊 Code Quality Analysis</h3>
                <ul>
                    <li><strong>Thread Safety:</strong> Excellent use of threading.local() for model instances</li>
                    <li><strong>Error Handling:</strong> Good fallback for spaCy model download</li>
                    <li><strong>Memory Efficiency:</strong> Generator-based file chunking prevents memory overflow</li>
                    <li><strong>Backend Abstraction:</strong> Clean strategy pattern for spaCy vs HuggingFace</li>
                </ul>

                <h3>🔧 Refactoring Recommendations</h3>
                <ul>
                    <li><strong>Remove Orphaned Dependencies:</strong> Remove tree-sitter packages from requirements.txt</li>
                    <li><strong>Consolidate Output Functions:</strong> Either use save_output() or remove it and keep inline saving</li>
                    <li><strong>Extract Configuration:</strong> Move model names and parameters to a config file</li>
                    <li><strong>Add Type Hints:</strong> Improve code documentation with proper type annotations</li>
                    <li><strong>Error Handling:</strong> Add more robust error handling for file I/O and model loading</li>
                </ul>

                <h3>📈 Performance Insights</h3>
                <ul>
                    <li><strong>Apple Silicon Optimization:</strong> Properly configured for MPS backend</li>
                    <li><strong>Threading Strategy:</strong> spaCy uses parallel workers, HuggingFace uses sequential processing</li>
                    <li><strong>Memory Management:</strong> Thread-local storage prevents model reloading</li>
                    <li><strong>I/O Efficiency:</strong> Chunked file processing for large inputs</li>
                </ul>

                <h3>🔄 Recent Activity</h3>
                <ul>
                    <li><strong>Last 30 Days:</strong> Active development with 15+ commits</li>
                    <li><strong>Recent Focus:</strong> Thread-safe aggregation, NER per chunk, Apple GPU enforcement</li>
                    <li><strong>Cleanup:</strong> Removed tree-sitter submodules and cleaned up generated files</li>
                    <li><strong>Stability:</strong> Core functionality appears stable with incremental improvements</li>
                </ul>
            </div>
        </div>
    </div>
    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            mermaid: {
              curve: 'basis'
            },
            htmlLabels: true
        });
    </script>
</body>
</html>
