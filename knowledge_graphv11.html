<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Codebase Knowledge Graph</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .metadata {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .diagram-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Codebase Knowledge Graph</h1>
        <p>Interactive visualization of code structure and relationships</p>
    </div>

    <div class="metadata">
        <h3>📊 Analysis Metadata</h3>
        <p><strong>Generated:</strong> 2025-07-02_14-33-32</p>
        <p><strong>Commit Hash:</strong> 1a78242b5f8c5d282d1975779b381079ad5bfea2</p>
        <p><strong>Total Files Analyzed:</strong> 11</p>
        <p><strong>Cache Status:</strong> Fresh analysis (commit changed)</p>
    </div>

    <div class="diagram-container">
        <pre class="mermaid">
graph TD
    %% File Subgraphs
    subgraph MainPy ["main.py [SourceFile]"]
        main_parse_args[parse_args]
        main_chunk_file[chunk_file]
        main_get_nlp[get_nlp]
        main_get_hf_ner_pipeline[get_hf_ner_pipeline]
        main_process_chunk_spacy[process_chunk_spacy]
        main_process_chunk_hf[process_chunk_hf]
        main_process_chunk[process_chunk]
        main_worker_thread[worker_thread]
        main_test_spacy_ner[test_spacy_ner]
        main_save_output[save_output]
        main_thread_local[thread_local]
    end

    subgraph RequirementsTxt ["requirements.txt [BuildFile]"]
        req_spacy["spacy with apple support"]
        req_torch[torch]
        req_transformers[transformers]
        req_tree_sitter["tree-sitter"]
        req_tree_sitter_python["tree-sitter-python"]
        req_tree_sitter_languages["tree-sitter-languages"]
    end

    subgraph OutputJson ["output.json [ConfigFile]"]
        output_input_file[input_file]
        output_chunk_size[chunk_size]
        output_worker_count[worker_count]
        output_total_chunks[total_chunks]
        output_total_entities[total_entities]
        output_total_concepts[total_concepts]
        output_entities[entities]
        output_concepts[concepts]
    end

    subgraph TasksJson ["tasks.json [ConfigFile]"]
        tasks_projects[projects]
        tasks_tasks[tasks]
        tasks_subtasks[subtasks]
    end

    subgraph DocFiles ["Documentation Files"]
        doc_knowledge_graph["knowledge_graphv10.html"]
        doc_gitignore[".gitignore"]
        doc_third_party["ThirdPartyNoticeText.txt"]
        doc_sample["sample.txt"]
        doc_spec["spec-prompt.md"]
        doc_inventory1["2025-07-02_12-08-15_code_inventory.json"]
        doc_inventory2["2025-07-02_12-18-06_code_inventory.json"]
    end

    %% Dependencies and Imports
    main_get_nlp -- "imports" --> req_spacy
    main_get_hf_ner_pipeline -- "imports" --> req_torch
    main_get_hf_ner_pipeline -- "imports" --> req_transformers
    main_test_spacy_ner -- "imports" --> req_spacy

    %% Function Call Relationships
    main_process_chunk -- "calls" --> main_process_chunk_spacy
    main_process_chunk -- "calls" --> main_process_chunk_hf
    main_process_chunk_spacy -- "calls" --> main_get_nlp
    main_process_chunk_hf -- "calls" --> main_get_hf_ner_pipeline
    main_worker_thread -- "calls" --> main_process_chunk

    %% Data Flow Relationships
    main_chunk_file -- "processes" --> doc_sample
    main_save_output -- "generates" --> output_input_file
    main_save_output -- "generates" --> output_chunk_size
    main_save_output -- "generates" --> output_worker_count
    main_save_output -- "generates" --> output_total_chunks
    main_save_output -- "generates" --> output_total_entities
    main_save_output -- "generates" --> output_total_concepts
    main_save_output -- "generates" --> output_entities
    main_save_output -- "generates" --> output_concepts

    %% Configuration Relationships
    main_parse_args -- "configures" --> main_chunk_file
    main_parse_args -- "configures" --> main_worker_thread

    %% Thread Safety
    main_thread_local -- "provides thread safety for" --> main_get_nlp
    main_thread_local -- "provides thread safety for" --> main_get_hf_ner_pipeline

    %% Input Data Sources
    doc_third_party -- "default input for" --> main_chunk_file
    doc_spec -- "defines requirements for" --> main_parse_args

    %% Styling
    classDef sourceFile fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef buildFile fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef configFile fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef docFile fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef function fill:#bbdefb,stroke:#1976d2
    classDef dependency fill:#f8bbd9,stroke:#c2185b
    classDef property fill:#c8e6c9,stroke:#388e3c

    class main_parse_args,main_chunk_file,main_get_nlp,main_get_hf_ner_pipeline,main_process_chunk_spacy,main_process_chunk_hf,main_process_chunk,main_worker_thread,main_test_spacy_ner,main_save_output function
    class req_spacy,req_torch,req_transformers,req_tree_sitter,req_tree_sitter_python,req_tree_sitter_languages dependency
    class output_input_file,output_chunk_size,output_worker_count,output_total_chunks,output_total_entities,output_total_concepts,output_entities,output_concepts,tasks_projects,tasks_tasks,tasks_subtasks property
    class doc_knowledge_graph,doc_gitignore,doc_third_party,doc_sample,doc_spec,doc_inventory1,doc_inventory2 docFile
        </pre>
    </div>

    <script type="module">
        import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>
