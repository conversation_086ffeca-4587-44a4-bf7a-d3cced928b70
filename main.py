import argparse
import threading
from queue import Queue
import spacy
import json
import os
from datetime import datetime
import torch
from transformers import pipeline

# Entry point for spaCy Apple Silicon PoC

def parse_args():
    parser = argparse.ArgumentParser(description="spaCy/HuggingFace Apple Silicon PoC")
    parser.add_argument('--chunk', type=int, default=1000, help='Chunk size for text processing')
    parser.add_argument('--worker', type=int, default=4, help='Number of worker threads')
    parser.add_argument('--input', type=str, default="sample.txt", help='Input text file to process')
    backend = parser.add_mutually_exclusive_group(required=False)
    backend.add_argument('--sp', action='store_true', help='Use spaCy backend (default)')
    backend.add_argument('--hf', action='store_true', help='Use Hugging Face transformers backend')
    return parser.parse_args()

def chunk_file(filepath, chunk_size):
    with open(filepath, 'r', encoding='utf-8') as f:
        buffer = []
        for line in f:
            buffer.append(line)
            if sum(len(line_) for line_ in buffer) >= chunk_size:
                yield ''.join(buffer)
                buffer = []
        if buffer:
            yield ''.join(buffer)

thread_local = threading.local()

def get_nlp():
    if not hasattr(thread_local, "nlp"):
        try:
            thread_local.nlp = spacy.load("en_core_web_sm")
        except OSError:
            from spacy.cli.download import download
            download("en_core_web_sm")
            thread_local.nlp = spacy.load("en_core_web_sm")
    return thread_local.nlp

def get_hf_ner_pipeline(global_hf_ner=None):
    # If a global pipeline is provided, just return it
    if global_hf_ner is not None:
        return global_hf_ner
    if not hasattr(thread_local, "hf_ner"):
        device = 0 if torch.backends.mps.is_available() else -1
        thread_local.hf_ner = pipeline("ner", model="dslim/bert-base-NER", device=device, aggregation_strategy="simple")
    return thread_local.hf_ner

def process_chunk_spacy(chunk, chunk_id):
    nlp = get_nlp()
    doc = nlp(chunk)
    allowed_labels = {"PERSON", "ORG", "PRODUCT"}
    entities = [
        {"text": ent.text, "label": ent.label_, "start": ent.start_char, "end": ent.end_char}
        for ent in doc.ents
        if ent.label_ in allowed_labels
    ]
    # Filtered noun chunks as concepts
    concepts = []
    for nc in doc.noun_chunks:
        # Exclude pronouns, determiners, stopwords, and very short/generic words
        if (
            nc.root.pos_ in {"PRON", "DET"}
            or nc.text.lower() in nlp.Defaults.stop_words
            or len(nc.text.strip()) < 3
        ):
            continue
        concepts.append({"text": nc.text, "start": nc.start_char, "end": nc.end_char})
    print(f"[Thread] Processed chunk {chunk_id} (entities: {len(entities)} concepts: {len(concepts)})")
    return {"chunk_id": chunk_id, "entities": entities, "concepts": concepts, "length": len(chunk)}

def process_chunk_hf(chunk, chunk_id, global_hf_ner=None):
    ner = get_hf_ner_pipeline(global_hf_ner)
    results = ner(chunk)
    entities = []
    if results:
        for r in results:
            entities.append({
                'text': r['word'] if 'word' in r else chunk[r['start']:r['end']],
                'label': r.get('entity_group', r.get('entity')),
                'start': r.get('start', 0),
                'end': r.get('end', 0)
            })
    # For concepts, use noun phrase-like extraction: just grab unique noun words as a simple fallback
    # (for demo, not as rich as spaCy)
    from collections import Counter
    import re
    words = re.findall(r'\b\w+\b', chunk)
    concepts = []
    word_counts = Counter([w.lower() for w in words if len(w) > 3])
    for w, count in word_counts.items():
        concepts.append({"text": w, "start": chunk.find(w), "end": chunk.find(w)+len(w)})
    print(f"[HF] Processed chunk {chunk_id} (entities: {len(entities)} concepts: {len(concepts)})")
    return {"chunk_id": chunk_id, "entities": entities, "concepts": concepts, "length": len(chunk)}

def process_chunk(chunk, chunk_id, backend):
    if backend == 'spacy':
        return process_chunk_spacy(chunk, chunk_id)
    else:
        return process_chunk_hf(chunk, chunk_id)

def worker_thread(q, results, results_lock, backend):
    while True:
        item = q.get()
        if item is None:
            break
        chunk_id, chunk = item
        result = process_chunk(chunk, chunk_id, backend)
        with results_lock:
            results.append(result)
        q.task_done()

def test_spacy_ner():
    print("Testing spaCy NER pipeline...")
    try:
        nlp = spacy.load("en_core_web_sm")
    except OSError:
        from spacy.cli.download import download
        download("en_core_web_sm")
        nlp = spacy.load("en_core_web_sm")
    doc = nlp("Apple is looking at buying U.K. startup for $1 billion")
    for ent in doc.ents:
        print(f"Entity: {ent.text}, Label: {ent.label_}")
    print("spaCy NER pipeline test complete.")

def save_output(entities):
    os.makedirs('output', exist_ok=True)
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M')
    filename = f'output/{timestamp}-output.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(entities, f, ensure_ascii=False, indent=2)
    print(f"Saved output to {filename}")

if __name__ == "__main__":
    args = parse_args()
    backend = 'spacy' if args.sp else 'hf'
    if backend == 'spacy':
        try:
            spacy.require_gpu()
            print("spaCy: Apple GPU is available and will be used.")
        except Exception:
            print("ERROR: spaCy could not access the Apple GPU. Exiting.")
            raise
        test_spacy_ner()
    elif backend == 'hf':
        print("Hugging Face backend selected. (NER/concept extraction will use transformers)")
    print(f"Chunk size: {args.chunk}, Workers: {args.worker}, Input: {args.input}")
    q = Queue()
    results = []
    results_lock = threading.Lock()
    threads = []
    if backend == 'hf':
        # Load pipeline ONCE in main thread
        global_hf_ner = pipeline("ner", model="dslim/bert-base-NER", device=0 if torch.backends.mps.is_available() else -1, aggregation_strategy="simple")
        # Process all chunks sequentially
        for i, chunk in enumerate(chunk_file(args.input, args.chunk)):
            result = process_chunk_hf(chunk, i+1, global_hf_ner)
            results.append(result)
        print(f"Processed {len(results)} chunks sequentially (Hugging Face mode).")
    else:
        for _ in range(args.worker):
            t = threading.Thread(target=worker_thread, args=(q, results, results_lock, backend))
            t.start()
            threads.append(t)
        for i, chunk in enumerate(chunk_file(args.input, args.chunk)):
            q.put((i+1, chunk))
        for _ in threads:
            q.put(None)
        for t in threads:
            t.join()
        print(f"Processed {len(results)} chunks in parallel.")
    # Aggregate all entities and concepts from all chunks
    all_entities = []
    all_concepts = []
    for chunk_result in results:
        all_entities.extend(chunk_result["entities"])
        all_concepts.extend(chunk_result["concepts"])

    # Dedupe entities by (text, label), case-insensitive
    seen_entities = set()
    deduped_entities = []
    for ent in all_entities:
        key = (ent["text"].strip().lower(), ent["label"])
        if key not in seen_entities:
            seen_entities.add(key)
            deduped_entities.append(ent)

    # Dedupe concepts by text, case-insensitive, and count frequency
    seen_concepts = {}
    for concept in all_concepts:
        key = concept["text"].strip().lower()
        if key not in seen_concepts:
            seen_concepts[key] = {"concept": concept, "count": 1}
        else:
            seen_concepts[key]["count"] += 1
    # Add importance and sort by frequency (importance)
    for item in seen_concepts.values():
        item["concept"]["importance"] = item["count"]
    sorted_concepts = sorted(seen_concepts.values(), key=lambda x: x["count"], reverse=True)
    top_n = max(1, len(sorted_concepts) // 2)
    deduped_concepts = [item["concept"] for item in sorted_concepts[:top_n]]

    output_data = {
        "input_file": args.input,
        "chunk_size": args.chunk,
        "worker_count": args.worker,
        "total_chunks": len(results),
        "total_entities": len(deduped_entities),
        "total_concepts": len(deduped_concepts),
        "entities": deduped_entities,
        "concepts": deduped_concepts  # Already sorted by importance
    }

    # Print or save the output_data as needed
    # print(json.dumps(output_data, indent=2))
    with open("output.json", "w", encoding="utf-8") as outfile:
        json.dump(output_data, outfile, ensure_ascii=False, indent=2)
    # Save both entities and concepts to the timestamped output file
    os.makedirs('output', exist_ok=True)
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M')
    backend_tag = 'sp' if backend == 'spacy' else 'hf'
    filename = f'output/{timestamp}-{backend_tag}-output.json'
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    print(f"Saved output to {filename}")
